#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import json

def read_existing_excel():
    """读取现有的Excel文件"""
    try:
        # 读取现有的Excel文件
        df = pd.read_excel("催收 2.0-YXL 男声-intentions (15).xls")
        print(f"成功读取现有Excel文件，包含 {len(df)} 行数据")
        print(f"Excel文件的列名: {df.columns.tolist()}")

        # 显示前几行数据以了解结构
        print("\n前5行数据:")
        print(df.head())

        return df
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def create_new_huashu_data():
    """创建新增的话术数据"""
    new_huashu = [
        {
            "id": "FY-032",
            "keywords": "",
            "content": "${gender_sisbro},这个已经逾期${overdueDates}天了,再不处理就要影响征信记录了,这笔钱也不多,赶紧想办法处理一下好吧?",
            "intentCategory": "敷衍/拒绝沟通",
            "level": "3"
        },
        {
            "id": "FY-033",
            "keywords": "",
            "content": "看得出来您之前都是很守信用的,就差最后几期,咱们一起想办法把它处理了好吧?",
            "intentCategory": "敷衍/拒绝沟通",
            "level": "2"
        },
        {
            "id": "FY-034",
            "keywords": "",
            "content": "看您之前都能按时还款,现在就剩这点了,要不先凑个几百块应付一下可以吧?",
            "intentCategory": "敷衍/拒绝沟通",
            "level": "2"
        },
        {
            "id": "JJ-037",
            "keywords": "",
            "content": "${gender_sisbro}，再不还款的话我们这边要录音备案,到时候天天电话短信催收,您也不好受，还是今天想办法还掉可以吧?",
            "intentCategory": "拒绝还款",
            "level": "4"
        },
        {
            "id": "XS-072",
            "keywords": "",
            "content": "您做生意虽然亏了,但现在慢慢在恢复了不是吗?千把块应该难不倒你，想想办法今天还掉好吧?",
            "intentCategory": "协商还款",
            "level": "2"
        },
        {
            "id": "FY-035",
            "keywords": "",
            "content": "这笔欠款很快就要移交专门的催收部门了,建议您尽快处理,免得麻烦和风险升级好吧?",
            "intentCategory": "敷衍/拒绝沟通",
            "level": "3"
        },
        {
            "id": "JJ-041",
            "keywords": "",
            "content": "${gender_sisbro},这笔钱再不还,你的风险可能升级，到时候不排除致电你的预留联系人核实你的情况，到时候多不好，你今天能处理掉不?",
            "intentCategory": "拒绝还款",
            "level": "4"
        },
        {
            "id": "JJ-042",
            "keywords": "",
            "content": "你的这个账户再不处理可能就会升级成高风险账户，甚至可能会影响你在平台的信用，导致你后期可能无法继续和平台合作，您考虑清楚，还想继续合作吗?",
            "intentCategory": "拒绝还款",
            "level": "4"
        },
        {
            "id": "JJ-043",
            "keywords": "",
            "content": "${name}现在不处理的话利息违约金会越来越多,不如今天想办法还掉好吧?",
            "intentCategory": "拒绝还款",
            "level": "3"
        },
        {
            "id": "JJ-044",
            "keywords": "",
            "content": "您要是一直拖着不还,,后期会有专员致电你预留联系人核实你的情况，，不如现在先找家人朋友周转一下，还是赶紧处理了好吧?",
            "intentCategory": "拒绝还款",
            "level": "4"
        },
        {
            "id": "XS-073",
            "keywords": "",
            "content": "看得出来您是个讲信用的人,咱们一起想办法把这期搞定好吧?",
            "intentCategory": "协商还款",
            "level": "2"
        },
        {
            "id": "FY-036",
            "keywords": "",
            "content": "${gender_sisbro}您就为这千把块征信弄花了多不值得，还是重视下，今天去周转下还掉好吧?",
            "intentCategory": "敷衍/拒绝沟通",
            "level": "3"
        },
        {
            "id": "FY-037",
            "keywords": "",
            "content": "我们相关逾期沟通都是要录音存档的,到时候催收流程会很麻烦，也请您知晓一下，今天想办法把欠款处理了好吧?",
            "intentCategory": "敷衍/拒绝沟通",
            "level": "4"
        },
        {
            "id": "FY-038",
            "keywords": "",
            "content": "${gender}你再不处理的话征信可能会受影响,你知道现在有的单位招聘也是要看征信的，如果你的征信不好到时候可能连找工作都会受影响。赶紧想想办法吧?",
            "intentCategory": "敷衍/拒绝沟通",
            "level": "4"
        }
    ]

    print(f"创建了 {len(new_huashu)} 条新话术数据")
    return new_huashu

def convert_to_excel_format(existing_df, new_huashu_data):
    """将新话术数据转换为Excel格式并合并"""

    # 分析现有Excel的列结构
    print(f"\n现有Excel列结构: {existing_df.columns.tolist()}")

    # 分析现有数据的模式，获取默认值
    sample_row = existing_df.iloc[0] if len(existing_df) > 0 else {}

    # 创建新数据的DataFrame
    new_rows = []

    for item in new_huashu_data:
        # 根据现有Excel的列结构创建新行
        new_row = {}

        # 精确映射到现有的列
        for col in existing_df.columns:
            if col == '任务ID':
                new_row[col] = sample_row.get('任务ID', 181)  # 使用现有的任务ID
            elif col == '意图Code':
                new_row[col] = item['id']
            elif col == '版本号':
                new_row[col] = '1.0'
            elif col == '备注':
                new_row[col] = None  # 保持为空
            elif col == '是否启用 (Y: 是, N: 否)':
                new_row[col] = 'Y'  # 新增的话术默认启用
            elif col == '意图名称':
                new_row[col] = f"{item['intentCategory']}/{item['id']}"
            elif col == '意图话术':
                new_row[col] = item['content']
            elif col == '是否包含参数':
                # 检查话术内容是否包含参数（如 ${name}, ${gender} 等）
                has_params = '${' in item['content']
                new_row[col] = 'Y' if has_params else 'N'
            elif col == '打断配置':
                new_row[col] = 5  # 使用默认值
            elif col == '归属标签':
                new_row[col] = None  # 保持为空
            elif col == '是否结束':
                new_row[col] = 'N'  # 默认不结束
            elif col == '拨打策略id':
                new_row[col] = 'two'  # 使用默认策略
            elif col == 'voiceId':
                # 根据意图分类设置不同的voiceId（参考现有数据模式）
                if item['intentCategory'] == '协商还款':
                    new_row[col] = 10400.0  # XS系列的voiceId范围
                elif item['intentCategory'] == '拒绝还款':
                    new_row[col] = 10500.0  # JJ系列的voiceId范围
                elif item['intentCategory'] == '敷衍/拒绝沟通':
                    new_row[col] = 10600.0  # FY系列的voiceId范围
                else:
                    new_row[col] = 10400.0  # 默认值
            else:
                # 对于其他列，设置为空值或默认值
                new_row[col] = ""

        new_rows.append(new_row)

    # 创建新数据的DataFrame
    new_df = pd.DataFrame(new_rows)

    # 合并数据
    combined_df = pd.concat([existing_df, new_df], ignore_index=True)

    print(f"合并后总计 {len(combined_df)} 行数据")

    # 显示新增数据的预览
    print("\n新增数据预览:")
    print(new_df[['意图Code', '意图名称', '意图话术', '是否包含参数', 'voiceId']].head())

    return combined_df

def save_new_excel(df, filename):
    """保存新的Excel文件"""
    try:
        # 保存为新的Excel文件
        df.to_excel(filename, index=False, engine='openpyxl')
        print(f"成功保存新的Excel文件: {filename}")

        # 显示保存后的统计信息
        print(f"新Excel文件包含 {len(df)} 行数据")
        print(f"列名: {df.columns.tolist()}")

    except Exception as e:
        print(f"保存Excel文件时出错: {e}")

def main():
    """主函数"""
    print("开始处理话术数据维护...")

    # 1. 读取现有Excel文件
    existing_df = read_existing_excel()
    if existing_df is None:
        return

    # 2. 创建新增话术数据
    new_huashu_data = create_new_huashu_data()

    # 3. 转换并合并数据
    combined_df = convert_to_excel_format(existing_df, new_huashu_data)

    # 4. 保存新的Excel文件
    new_filename = "催收 2.0-YXL 男声-intentions (16).xlsx"
    save_new_excel(combined_df, new_filename)

    print("话术数据维护完成！")

if __name__ == "__main__":
    main()
