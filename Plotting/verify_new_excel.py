#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def verify_excel_file():
    """验证新生成的Excel文件"""
    try:
        # 读取新生成的Excel文件
        df = pd.read_excel("催收 2.0-YXL 男声-intentions (16).xlsx")
        
        print(f"✅ 成功读取Excel文件，包含 {len(df)} 行数据")
        print(f"📊 列名: {df.columns.tolist()}")
        
        # 查找新增的话术（FY-032到FY-038, JJ-037到JJ-044, XS-072到XS-073）
        new_ids = ['FY-032', 'FY-033', 'FY-034', 'JJ-037', 'XS-072', 'FY-035', 
                   'JJ-041', 'JJ-042', 'JJ-043', 'JJ-044', 'XS-073', 'FY-036', 
                   'FY-037', 'FY-038']
        
        new_data = df[df['意图Code'].isin(new_ids)]
        
        print(f"\n🎯 找到新增话术 {len(new_data)} 条:")
        
        # 按意图分类统计
        category_stats = new_data.groupby('意图名称').size()
        print("\n📈 按意图分类统计:")
        for category, count in category_stats.items():
            print(f"  - {category}: {count}条")
        
        # 检查参数化话术
        param_data = new_data[new_data['是否包含参数'] == 'Y']
        print(f"\n🔧 包含参数的话术: {len(param_data)}条")
        for _, row in param_data.iterrows():
            print(f"  - {row['意图Code']}: {row['意图话术'][:50]}...")
        
        # 检查voiceId分配
        print(f"\n🎵 voiceId分配:")
        voice_stats = new_data.groupby('voiceId').size()
        for voice_id, count in voice_stats.items():
            print(f"  - voiceId {voice_id}: {count}条")
        
        # 显示完整的新增数据
        print(f"\n📋 新增话术详细信息:")
        for _, row in new_data.iterrows():
            print(f"  {row['意图Code']} | {row['意图名称']} | 参数:{row['是否包含参数']} | voiceId:{row['voiceId']}")
            print(f"    话术: {row['意图话术']}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 验证Excel文件时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔍 开始验证新生成的Excel文件...")
    
    if verify_excel_file():
        print("✅ Excel文件验证完成！")
    else:
        print("❌ Excel文件验证失败！")

if __name__ == "__main__":
    main()
